[package]
name = "maia"
version = "0.1.0"
edition = "2021"
description = "A modular Rust library for multi-agent conversations with AI backends"
authors = ["<PERSON> <<EMAIL>>"]
license = "MIT"
readme = "README.md"
repository = "https://github.com/your-username/maia"
keywords = ["ai", "agents", "chat", "claude", "conversation"]
categories = ["api-bindings", "command-line-utilities"]

[[bin]]
name = "maia"
path = "src/main.rs"

[lib]
name = "maia"
path = "src/lib.rs"

[dependencies]
anyhow = "1.0.98"
chrono = { version = "0.4.41", features = ["serde"] }
reqwest = { version = "0.12.15", features = ["json", "blocking"] }
serde = { version = "1.0.219", features = ["derive"] }
serde_yaml = "0.9.34"
tokio = { version = "1.45.1", features = ["full"] }
