# Maia - Multi AI Agent Chat Application

A modular Rust application for multi-agent conversations with AI backends.

## Architecture

The application is organized into domain-specific modules:

### 📁 Module Structure

```
src/
├── main.rs           # Application entry point
├── models.rs         # Core domain models
├── agents.rs         # Agent implementations
├── backends.rs       # AI backend implementations
├── claude_api.rs     # Claude API integration
└── room.rs           # Conversation management
```

### 🏗️ Domain Separation

#### **`models.rs` - Core Domain Models**
- `Human` - Represents human users
- `Message` - Individual conversation messages
- `MessageAuthor` - Message authorship (Human, Agent, System)

#### **`agents.rs` - Agent Implementations**
- `Agent` trait - Interface for conversational agents
- `GeneralistAgent` - General-purpose agent
- `SpecialistAgent` - Domain-specific agent (example)

#### **`backends.rs` - AI Backend Services**
- `Backend` trait - Interface for AI services
- `ClaudeBackend` - Anthropic Claude API integration
- `MockBackend` - Testing/demonstration backend

#### **`claude_api.rs` - Claude API Integration**
- `ClaudeMessage`, `ClaudeRequest`, `ClaudeResponse` - API data structures
- Configuration constants and utilities

#### **`room.rs` - Conversation Management**
- `Room` - Manages conversations between humans and agents
- Message routing and agent coordination

## Features

- **🔌 Pluggable Backends**: Easy to add new AI services
- **🤖 Multiple Agent Types**: Generalist and specialist agents
- **🧪 Testing Support**: Mock backend for development
- **📝 Rich Conversation**: Timestamped messages with authorship
- **🎯 Clean Architecture**: Domain-driven design with clear separation

## Usage

### Basic Example

```rust
use maia::{
    agents::GeneralistAgent,
    backends::{MockBackend, ClaudeBackend},
    models::Human,
    room::Room,
};

// Create a human user
let human = Human { name: "Alice".to_string(), age: 30 };

// Create a mock backend for testing
let backend = MockBackend::new(vec!["Hello!".to_string()]);
let agent = GeneralistAgent::new("Assistant".to_string(), Box::new(backend));

// Create a conversation room
let mut room = Room::new(human, vec![Box::new(agent)]);

// Send a message
room.send_human_message("Hello, how are you?".to_string())?;

// Print the conversation
room.print_conversation();
```

### With Claude Backend

```rust
// Create Claude backend with API key
let claude_backend = ClaudeBackend::new(api_key);
let claude_agent = GeneralistAgent::new("Claude".to_string(), Box::new(claude_backend));

let mut room = Room::new(human, vec![Box::new(claude_agent)]);
room.send_human_message("What's the weather like?".to_string())?;
```

## Configuration

Set the `ANTHROPIC_API_KEY` environment variable to use the Claude backend:

```bash
export ANTHROPIC_API_KEY="your-api-key-here"
cargo run
```

## Dependencies

- `anyhow` - Error handling
- `chrono` - Date/time handling
- `reqwest` - HTTP client for API calls
- `serde` - Serialization/deserialization
- `serde_yaml` - YAML support
- `tokio` - Async runtime

## Development

### Running Tests

```bash
cargo test
```

### Running with Mock Backend

```bash
cargo run
```

### Running with Claude Backend

```bash
export ANTHROPIC_API_KEY="your-key"
cargo run
```

## Extending the System

### Adding a New Backend

1. Implement the `Backend` trait:

```rust
#[derive(Debug)]
struct MyBackend {
    // backend-specific fields
}

impl Backend for MyBackend {
    fn generate_response(&self, messages: &[Message], system_prompt: &str) -> Result<String> {
        // implementation
    }
}
```

2. Use it with any agent:

```rust
let backend = MyBackend::new();
let agent = GeneralistAgent::new("MyAgent".to_string(), Box::new(backend));
```

### Adding a New Agent Type

1. Implement the `Agent` trait:

```rust
#[derive(Debug)]
struct MyAgent {
    backend: Box<dyn Backend>,
}

impl Agent for MyAgent {
    fn name(&self) -> &str { "MyAgent" }
    fn system_prompt(&self) -> &str { "Custom prompt" }
    fn should_respond(&self, messages: &[Message]) -> bool { true }
    fn respond(&self, messages: &[Message]) -> Result<String> {
        self.backend.generate_response(messages, self.system_prompt())
    }
}
```

## License

MIT License - see LICENSE file for details.
