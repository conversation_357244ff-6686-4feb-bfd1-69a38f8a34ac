//! Backend implementations for AI services.
//! 
//! This module contains the Backend trait and various implementations
//! for different AI services (<PERSON>, <PERSON><PERSON>, etc.).

use std::fmt::Debug;
use anyhow::Result;

use crate::models::{Message, MessageAuthor};
use crate::claude_api::{<PERSON><PERSON><PERSON>, ClaudeRequest, ClaudeResponse, config};

/// Trait for AI backend services
pub trait Backend: Debug {
    /// Generate a response based on conversation history and system prompt
    fn generate_response(&self, messages: &[Message], system_prompt: &str) -> Result<String>;
}

/// Claude API backend implementation
#[derive(Debug)]
pub struct ClaudeBackend {
    api_key: String,
    model: String,
    max_tokens: u32,
}

impl ClaudeBackend {
    /// Creates a new Claude backend with the given API key
    pub fn new(api_key: String) -> Self {
        Self {
            api_key,
            model: config::DEFAULT_MODEL.to_string(),
            max_tokens: config::DEFAULT_MAX_TOKENS,
        }
    }
    
    /// Creates a new Claude backend with custom configuration
    pub fn with_config(api_key: String, model: String, max_tokens: u32) -> Self {
        Self {
            api_key,
            model,
            max_tokens,
        }
    }
    
    /// Converts internal messages to Claude API format
    fn convert_messages(&self, messages: &[Message]) -> Vec<ClaudeMessage> {
        messages
            .iter()
            .filter_map(|msg| match &msg.author {
                MessageAuthor::Human => Some(ClaudeMessage::user(msg.text.clone())),
                MessageAuthor::Agent(_) => Some(ClaudeMessage::assistant(msg.text.clone())),
                MessageAuthor::System => None, // System messages are handled separately
            })
            .collect()
    }
}

impl Backend for ClaudeBackend {
    fn generate_response(&self, messages: &[Message], system_prompt: &str) -> Result<String> {
        let client = reqwest::blocking::Client::new();
        
        let claude_messages = self.convert_messages(messages);
        
        let request = ClaudeRequest::new(
            self.model.clone(),
            self.max_tokens,
            claude_messages,
            Some(system_prompt.to_string()),
        );
        
        let response = client
            .post(config::API_URL)
            .header("x-api-key", &self.api_key)
            .header("anthropic-version", config::API_VERSION)
            .header("content-type", "application/json")
            .json(&request)
            .send()?;
        
        let claude_response: ClaudeResponse = response.json()?;
        
        // Extract text from the first content block
        Ok(claude_response
            .extract_text()
            .unwrap_or("No response from Claude")
            .to_string())
    }
}

/// Mock backend for testing and demonstration
#[derive(Debug)]
pub struct MockBackend {
    responses: Vec<String>,
    current_index: std::cell::RefCell<usize>,
}

impl MockBackend {
    /// Creates a new mock backend with predefined responses
    pub fn new(responses: Vec<String>) -> Self {
        Self {
            responses,
            current_index: std::cell::RefCell::new(0),
        }
    }
    
    /// Creates a mock backend with default responses
    pub fn default() -> Self {
        Self::new(vec![
            "Hello! How can I help you?".to_string(),
            "That's interesting! Tell me more.".to_string(),
            "I understand. Is there anything else I can help with?".to_string(),
        ])
    }
}

impl Backend for MockBackend {
    fn generate_response(&self, messages: &[Message], _system_prompt: &str) -> Result<String> {
        let mut index = self.current_index.borrow_mut();
        let response = if *index < self.responses.len() {
            self.responses[*index].clone()
        } else {
            let last_message = messages
                .last()
                .map(|m| m.text.as_str())
                .unwrap_or("(no message)");
            format!("Mock response to: {}", last_message)
        };
        *index += 1;
        Ok(response)
    }
}
