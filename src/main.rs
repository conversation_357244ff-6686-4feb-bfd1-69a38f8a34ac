mod agents;
mod backends;
mod claude_api;
mod models;
mod room;

use std::fs::File;

use agents::Agent;
use agents::GeneralistAgent;
use anyhow::Result;
use backends::Backend;
use backends::ClaudeBackend;
use backends::MockBackend;
use chrono::Utc;
use models::Human;
use room::Room;

fn main() -> Result<()> {
    // Load Human data from human.yaml at runtime
    let human: Human = serde_yaml::from_reader(File::open("human.yaml")?)?;

    println!("=== Human Data ===");
    println!("{:?}", human);

    // Demonstrate with mock backend first
    println!("\n=== Testing with Mock Backend ===");
    let mock_backend = MockBackend::new(vec![
        "Hello! I'm doing great, thanks for asking!".to_string(),
        "How can I help you today?".to_string(),
    ]);

    let mock_agent = GeneralistAgent::new("MockAgent".to_string(), Box::new(mock_backend));
    let mut mock_room = Room::new(human.clone(), vec![Box::new(mock_agent)]);

    // Send messages using the new API
    mock_room.send_human_message("Hello, how are you?".to_string())?;
    mock_room.send_human_message("What can you do?".to_string())?;

    // Print conversation using the new method
    mock_room.print_conversation();

    // Try with Claude backend if API key is available
    if let Ok(anthropic_api_key) = std::env::var("ANTHROPIC_API_KEY") {
        if anthropic_api_key != "test-key" {
            println!("\n=== Testing with Claude Backend ===");
            let claude_backend = ClaudeBackend::new(anthropic_api_key);
            let claude_agent = GeneralistAgent::new("Claude".to_string(), Box::new(claude_backend));
            let mut claude_room = Room::new(human, vec![Box::new(claude_agent)]);

            claude_room.send_human_message(format!(
                "Hello, Claude! What happened today in previous years? Today is {}.",
                Utc::now().format("%B %d, %Y")
            ))?;

            claude_room.print_conversation();
        } else {
            println!("\n=== Claude Backend (Test Mode) ===");
            println!("Using test API key - would make real API call with valid key");
        }
    } else {
        println!("\n=== Claude Backend ===");
        println!("ANTHROPIC_API_KEY not set - skipping Claude backend test");
    }

    Ok(())
}
