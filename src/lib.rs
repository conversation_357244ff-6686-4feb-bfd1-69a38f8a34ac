//! Maia - Multi AI Agent Chat Application
//! 
//! A modular Rust library for building multi-agent conversations with AI backends.
//! 
//! ## Architecture
//! 
//! The library is organized into domain-specific modules:
//! 
//! - [`models`] - Core domain models (Human, Message, MessageAuthor)
//! - [`agents`] - Agent implementations and traits
//! - [`backends`] - AI backend services (<PERSON>, <PERSON><PERSON>, etc.)
//! - [`claude_api`] - Claude API integration utilities
//! - [`room`] - Conversation management
//! 
//! ## Quick Start
//! 
//! ```rust
//! use maia::{
//!     agents::GeneralistAgent,
//!     backends::MockBackend,
//!     models::Human,
//!     room::Room,
//! };
//! 
//! # fn main() -> anyhow::Result<()> {
//! // Create a human user
//! let human = Human { name: "Alice".to_string(), age: 30 };
//! 
//! // Create a mock backend for testing
//! let backend = MockBackend::new(vec!["Hello there!".to_string()]);
//! let agent = GeneralistAgent::new("Assistant".to_string(), Box::new(backend));
//! 
//! // Create a conversation room
//! let mut room = Room::new(human, vec![Box::new(agent)]);
//! 
//! // Send a message
//! room.send_human_message("Hello, how are you?".to_string())?;
//! 
//! // Print the conversation
//! room.print_conversation();
//! # Ok(())
//! # }
//! ```

pub mod models;
pub mod agents;
pub mod backends;
pub mod claude_api;
pub mod room;

// Re-export commonly used types for convenience
pub use models::{Human, Message, MessageAuthor};
pub use agents::{Agent, GeneralistAgent};
pub use backends::{Backend, ClaudeBackend, MockBackend};
pub use room::Room;
