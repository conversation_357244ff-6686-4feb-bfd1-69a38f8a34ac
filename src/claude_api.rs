//! Claude API integration module.
//! 
//! This module contains all the data structures and utilities needed
//! to interact with the Anthropic Claude API.

use serde::{Deserialize, Serialize};

/// Represents a message in Claude API format
#[derive(Debug, Serialize, Deserialize)]
pub struct ClaudeMessage {
    pub role: String,
    pub content: String,
}

impl ClaudeMessage {
    /// Creates a user message
    pub fn user(content: String) -> Self {
        Self {
            role: "user".to_string(),
            content,
        }
    }
    
    /// Creates an assistant message
    pub fn assistant(content: String) -> Self {
        Self {
            role: "assistant".to_string(),
            content,
        }
    }
}

/// Request structure for Claude API
#[derive(Debug, Serialize)]
pub struct ClaudeRequest {
    pub model: String,
    pub max_tokens: u32,
    pub messages: Vec<ClaudeMessage>,
    pub system: Option<String>,
}

impl ClaudeRequest {
    /// Creates a new Claude API request
    pub fn new(
        model: String,
        max_tokens: u32,
        messages: Vec<ClaudeMessage>,
        system: Option<String>,
    ) -> Self {
        Self {
            model,
            max_tokens,
            messages,
            system,
        }
    }
}

/// Content block in Claude API response
#[derive(Debug, Deserialize)]
pub struct ClaudeContent {
    #[serde(rename = "type")]
    pub content_type: String,
    pub text: String,
}

/// Response structure from Claude API
#[derive(Debug, Deserialize)]
pub struct ClaudeResponse {
    pub content: Vec<ClaudeContent>,
}

impl ClaudeResponse {
    /// Extracts the text from the first content block
    pub fn extract_text(&self) -> Option<&str> {
        self.content.first().map(|content| content.text.as_str())
    }
}

/// Claude API configuration constants
pub mod config {
    pub const API_URL: &str = "https://api.anthropic.com/v1/messages";
    pub const API_VERSION: &str = "2023-06-01";
    pub const DEFAULT_MODEL: &str = "claude-3-7-sonnet-20250219";
    pub const DEFAULT_MAX_TOKENS: u32 = 1024;
}
