//! Room module for managing conversations.
//! 
//! This module contains the Room struct which manages conversations
//! between humans and AI agents.

use anyhow::Result;
use chrono::Utc;

use crate::models::{Human, Message, MessageAuthor};
use crate::agents::Agent;

/// Represents a conversation room with humans and agents
#[derive(Debug)]
pub struct Room {
    pub agents: Vec<Box<dyn Agent>>,
    pub human: Human,
    pub messages: Vec<Message>,
}

impl Room {
    /// Creates a new room with the given human and agents
    pub fn new(human: Human, agents: Vec<Box<dyn Agent>>) -> Self {
        Self {
            agents,
            human,
            messages: Vec::new(),
        }
    }
    
    /// Sends a message to the room and triggers agent responses
    pub fn send_message(&mut self, message: Message) -> Result<()> {
        self.messages.push(message);
        
        // Check with all agents, in order, which would respond to the new message
        for agent in &self.agents {
            if agent.should_respond(&self.messages) {
                // Get the response from the agent
                match agent.respond(&self.messages) {
                    Ok(response_text) => {
                        // Add the agent's response as a new message
                        let agent_message = Message {
                            text: response_text,
                            author: MessageAuthor::Agent(agent.name().to_string()),
                            timestamp: Utc::now(),
                        };
                        self.messages.push(agent_message);
                        break; // Only let the first responding agent reply
                    }
                    Err(e) => {
                        eprintln!("Error getting response from agent {}: {}", agent.name(), e);
                    }
                }
            }
        }
        Ok(())
    }
    
    /// Sends a human message to the room
    pub fn send_human_message(&mut self, text: String) -> Result<()> {
        let message = Message::human(text);
        self.send_message(message)
    }
    
    /// Sends a system message to the room
    pub fn send_system_message(&mut self, text: String) -> Result<()> {
        let message = Message::system(text);
        self.send_message(message)
    }
    
    /// Gets all messages in the conversation
    pub fn get_messages(&self) -> &[Message] {
        &self.messages
    }
    
    /// Gets the last message in the conversation
    pub fn get_last_message(&self) -> Option<&Message> {
        self.messages.last()
    }
    
    /// Clears all messages from the room
    pub fn clear_messages(&mut self) {
        self.messages.clear();
    }
    
    /// Gets the number of messages in the conversation
    pub fn message_count(&self) -> usize {
        self.messages.len()
    }
    
    /// Prints the conversation in a formatted way
    pub fn print_conversation(&self) {
        println!("=== Conversation ===");
        for message in &self.messages {
            match &message.author {
                MessageAuthor::Human => println!("👤 Human: {}", message.text),
                MessageAuthor::Agent(name) => println!("🤖 {}: {}", name, message.text),
                MessageAuthor::System => println!("⚙️  System: {}", message.text),
            }
        }
    }
}
