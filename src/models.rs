//! Core domain models for the multi-agent chat application.
//! 
//! This module contains the fundamental data structures that represent
//! the core entities in the system: humans, messages, and message authors.

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};

/// Represents a human user in the system
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Human {
    pub name: String,
    pub age: u8,
}

/// Represents the author of a message
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub enum MessageAuthor {
    /// System-generated message
    System,
    /// Message from a human user
    Human,
    /// Message from an AI agent (stores agent name)
    Agent(String),
}

/// Represents a message in the conversation
#[derive(Debug)]
pub struct Message {
    pub text: String,
    pub author: MessageAuthor,
    pub timestamp: DateTime<Utc>,
}

impl Message {
    /// Creates a new message
    pub fn new(text: String, author: MessageAuthor) -> Self {
        Self {
            text,
            author,
            timestamp: Utc::now(),
        }
    }
    
    /// Creates a human message
    pub fn human(text: String) -> Self {
        Self::new(text, MessageAuthor::Human)
    }
    
    /// Creates an agent message
    pub fn agent(text: String, agent_name: String) -> Self {
        Self::new(text, MessageAuthor::Agent(agent_name))
    }
    
    /// Creates a system message
    pub fn system(text: String) -> Self {
        Self::new(text, MessageAuthor::System)
    }
}
