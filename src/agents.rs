//! Agent implementations and traits.
//! 
//! This module contains the Agent trait and various agent implementations
//! that handle different types of conversations and behaviors.

use std::fmt::Debug;
use anyhow::Result;

use crate::models::Message;
use crate::backends::Backend;

/// Trait for conversational agents
pub trait Agent: Debug {
    /// Returns the agent's name
    fn name(&self) -> &str;
    
    /// Returns the agent's system prompt
    fn system_prompt(&self) -> &str;
    
    /// Determines if the agent should respond to the current conversation
    fn should_respond(&self, messages: &[Message]) -> bool;
    
    /// Generates a response to the conversation
    fn respond(&self, messages: &[Message]) -> Result<String>;
}

/// A generalist agent that can handle various types of conversations
#[derive(Debug)]
pub struct GeneralistAgent {
    name: String,
    backend: Box<dyn Backend>,
}

impl GeneralistAgent {
    /// Creates a new generalist agent with the given name and backend
    pub fn new(name: String, backend: Box<dyn Backend>) -> Self {
        Self { name, backend }
    }
}

impl Agent for GeneralistAgent {
    fn name(&self) -> &str {
        &self.name
    }
    
    fn system_prompt(&self) -> &str {
        "You're a generalist agent and should help the human with their questions and requests. \
         Be helpful, informative, and engaging in your responses."
    }
    
    fn should_respond(&self, _messages: &[Message]) -> bool {
        // Business logic: GeneralistAgent always responds
        true
    }
    
    fn respond(&self, messages: &[Message]) -> Result<String> {
        // Delegate to backend for AI generation
        self.backend.generate_response(messages, self.system_prompt())
    }
}

/// A specialized agent that only responds to specific topics
#[derive(Debug)]
pub struct SpecialistAgent {
    name: String,
    backend: Box<dyn Backend>,
    specialty: String,
    keywords: Vec<String>,
}

impl SpecialistAgent {
    /// Creates a new specialist agent
    pub fn new(
        name: String,
        backend: Box<dyn Backend>,
        specialty: String,
        keywords: Vec<String>,
    ) -> Self {
        Self {
            name,
            backend,
            specialty,
            keywords,
        }
    }
}

impl Agent for SpecialistAgent {
    fn name(&self) -> &str {
        &self.name
    }
    
    fn system_prompt(&self) -> &str {
        // This would typically be stored as a field, but for simplicity we'll generate it
        "You are a specialist agent. Focus on your area of expertise and provide detailed, \
         accurate information within your domain."
    }
    
    fn should_respond(&self, messages: &[Message]) -> bool {
        // Only respond if the last message contains one of our keywords
        if let Some(last_message) = messages.last() {
            let text_lower = last_message.text.to_lowercase();
            self.keywords.iter().any(|keyword| text_lower.contains(&keyword.to_lowercase()))
        } else {
            false
        }
    }
    
    fn respond(&self, messages: &[Message]) -> Result<String> {
        // Create a specialized system prompt
        let system_prompt = format!(
            "You are a specialist in {}. {}",
            self.specialty,
            self.system_prompt()
        );
        
        self.backend.generate_response(messages, &system_prompt)
    }
}
